interface UploadProgress {
  completedParts: number
  totalParts: number
  percentage: number
}

interface UploadOptions {
  onProgress?: (progress: UploadProgress) => void
  metadata?: Record<string, string>
  partSize?: number
  maxConcurrentUploads?: number
  retry?: number | false
  retryDelay?: number
}

interface ClientOptions {
  url: string
  metadata?: Record<string, string>
  partSize?: number
  maxConcurrentUploads?: number
  retry?: number | false
  retryDelay?: number
}

interface CreateResponse {
  uploadId: string
}

interface UploadPartResponse {
  partNumber: number
  etag: string
}

interface CompleteResponse {
  key: string
  etag: string
  location: string
}

class MultipartUploadClient {
  static async create(key: string, options: ClientOptions) {
    const baseURL = options.url.replace(/\/$/, '')
    const got = $fetch.create({
      baseURL,
      retry: options.retry ?? 3,
      retryDelay: options.retryDelay ?? 1000,
    })

    const { uploadId } = await got<CreateResponse>(`/${key}`, {
      method: 'POST',
      body: { metadata: options.metadata },
    })
    return new MultipartUploadClient(got.create({
      baseURL: `${baseURL}/${key}/${uploadId}`,
    }))
  }

  constructor(private readonly fetch: typeof $fetch) {
  }

  async uploadPart(partNumber: number, body: Blob) {
    return this.fetch<UploadPartResponse>(`/${partNumber}`, {
      method: 'PUT',
      body,
    })
  }

  async complete(parts: UploadPartResponse[]) {
    return this.fetch<CompleteResponse>(`/`, {
      method: 'POST',
      body: { parts: parts.sort((a, b) => a.partNumber - b.partNumber) },
    })
  }

  async abort() {
    await this.fetch(`/`, { method: 'DELETE' })
  }
}

/**
 * Upload a file using multipart upload
 */
export async function uploadFile(file: File | Blob, key: string, { onProgress, ...options }: UploadOptions = {}) {
  const partSize = options.partSize || 10 * 1024 * 1024 // Default 10MB parts
  const maxConcurrentUploads = options.maxConcurrentUploads || 5

  let client: MultipartUploadClient | undefined

  try {
    client = await MultipartUploadClient.create(key, { url: '/api/upload', ...options })

    const totalParts = Math.ceil(file.size / partSize)
    const parts: UploadPartResponse[] = []

    const uploadPromises: Promise<UploadPartResponse>[] = []
    let completedParts = 0

    for (let partNumber = 1; partNumber <= totalParts; partNumber++) {
      const start = (partNumber - 1) * partSize
      const end = Math.min(start + partSize, file.size)
      const partData = file.slice(start, end)

      const uploadPromise = client.uploadPart(partNumber, partData).then((result) => {
        completedParts++
        if (onProgress) {
          onProgress({
            completedParts,
            totalParts,
            percentage: Math.round((completedParts / totalParts) * 100),
          })
        }
        return result
      })

      uploadPromises.push(uploadPromise)

      // Control concurrency
      if (uploadPromises.length >= maxConcurrentUploads) {
        const completedPart = await Promise.race(uploadPromises)
        parts.push(completedPart)
        const completedIndex = uploadPromises.findIndex(p => p === Promise.resolve(completedPart))
        if (completedIndex !== -1) {
          uploadPromises.splice(completedIndex, 1)
        }
      }
    }

    // Wait for remaining uploads
    const remainingParts = await Promise.all(uploadPromises)
    parts.push(...remainingParts)

    return await client.complete(parts)
  }
  catch (error) {
    console.error('Multipart upload failed:', error)
    // Attempt to abort the upload on failure
    if (client) {
      try {
        await client.abort()
      }
      catch (abortError) {
        console.error('Failed to abort multipart upload:', abortError)
      }
    }
    throw error
  }
}
